import { db, decimal } from '../../utils/model';
import { HttpError } from '../../utils/httpError';
import {
  staffHasPermission,
  PERMISSIONS,
  createStaffAuthHelper,
} from '../../utils/permission';
import {
  transactionStatus,
  transactionType,
  createDateFilter,
  generateOrderNumber,
} from '../../utils/util';
import * as bcrypt from 'bcryptjs';
import { enqueueSendEmailJob } from '../../jobs/queueJobs/queues/emailQueueJob';
import dayjs from 'dayjs';
import { getMainSystemAccount } from '../system';
import { sendToUser, broadcast } from '../socket';
import { notificationService } from '../notification';
import { logger } from '../../utils/logger';
import { timestamp, generateCode } from '../../utils/util';

export const transactionService = {
  getAllTransactions: async (staffId: any, query: any) => {
    try {
      const auth = createStaffAuthHelper(staffId);

      const canManage = await auth.hasPermission(PERMISSIONS.TRANSACTION_EDIT);
      if (!canManage) {
        throw new HttpError('Unauthorized', 403);
      }

      const hasLocationAll = await auth.hasPermission(PERMISSIONS.LOCATION_ALL);
      const hasRegion = await auth.hasPermission(PERMISSIONS.LOCATION_REGION);

      let locationFilter: any = {};
      if (hasLocationAll) {
        // No location filter - get all data
      } else if (hasRegion) {
        const regionId = await auth.getRegionId();
        locationFilter = {
          location: {
            regionId,
          },
        };
      } else {
        const locationId = await auth.getLocationId();
        locationFilter = { locationId };
      }

      // Parse pagination parameters with defaults
      const page: number = parseInt(query.page as string) || 1;
      const limit: number = parseInt(query.limit as string) || 10;

      // Parse filter parameters
      const status = query.status;
      const type = query.type;
      const search = query.search as string;
      const startDate = query.startDate as string;
      const endDate = query.endDate as string;

      // Validate filter parameters
      if (status && !transactionStatus.includes(status.toUpperCase())) {
        throw new HttpError('Invalid transaction status', 400);
      }
      if (type && !transactionType.includes(type.toUpperCase())) {
        throw new HttpError('Invalid transaction type', 400);
      }

      // Create date filter using the reusable function
      const dateFilter = createDateFilter(startDate, endDate);

      // Build where clause with all filters
      const whereClause: any = {
        ...(search
          ? {
              OR: [
                { reference: { contains: search, mode: 'insensitive' } },
                { mode: { contains: search, mode: 'insensitive' } },
                {
                  fromAccount: {
                    OR: [
                      {
                        user: {
                          OR: [
                            {
                              emailAddress: {
                                contains: search,
                                mode: 'insensitive',
                              },
                            },
                            {
                              phoneNumber: {
                                contains: search,
                                mode: 'insensitive',
                              },
                            },
                            { uhid: { contains: search, mode: 'insensitive' } },
                          ],
                        },
                      },
                      {
                        staff: {
                          OR: [
                            {
                              email: { contains: search, mode: 'insensitive' },
                            },
                            {
                              phoneNumber: {
                                contains: search,
                                mode: 'insensitive',
                              },
                            },
                            {
                              fullName: {
                                contains: search,
                                mode: 'insensitive',
                              },
                            },
                          ],
                        },
                      },
                    ],
                  },
                },
                {
                  toAccount: {
                    OR: [
                      {
                        user: {
                          OR: [
                            {
                              emailAddress: {
                                contains: search,
                                mode: 'insensitive',
                              },
                            },
                            {
                              phoneNumber: {
                                contains: search,
                                mode: 'insensitive',
                              },
                            },
                            { uhid: { contains: search, mode: 'insensitive' } },
                          ],
                        },
                      },
                      {
                        staff: {
                          OR: [
                            {
                              email: { contains: search, mode: 'insensitive' },
                            },
                            {
                              phoneNumber: {
                                contains: search,
                                mode: 'insensitive',
                              },
                            },
                            {
                              fullName: {
                                contains: search,
                                mode: 'insensitive',
                              },
                            },
                          ],
                        },
                      },
                    ],
                  },
                },
              ],
            }
          : {}),
        ...(status ? { status: status.toUpperCase() } : {}),
        ...(type ? { type: type.toUpperCase() } : {}),
        ...dateFilter,
        ...locationFilter,
      };

      const [transactions, totalPages, totalCount] = await db.$transaction([
        db.transaction.findMany({
          where: whereClause,
          orderBy: { createdAt: 'desc' },
          include: {
            fromAccount: {
              select: {
                user: {
                  select: {
                    emailAddress: true,
                    phoneNumber: true,
                    firstName: true,
                    uhid: true,
                  },
                },
                staff: {
                  select: {
                    fullName: true,
                    staffCode: true,
                  },
                },
                system: {
                  select: {
                    name: true,
                  },
                },
              },
            },
            toAccount: {
              select: {
                user: {
                  select: {
                    emailAddress: true,
                    phoneNumber: true,
                    firstName: true,
                    uhid: true,
                  },
                },
                staff: {
                  select: {
                    fullName: true,
                    staffCode: true,
                  },
                },
                system: {
                  select: {
                    name: true,
                  },
                },
              },
            },
          },
          skip: (page - 1) * limit,
          take: limit,
        }),
        db.transaction.count({
          where: whereClause,
        }),
        db.transaction.count(),
      ]);

      return {
        transactions: transactions,
        totalPages: Math.ceil(totalPages / limit),
        totalCount: totalCount,
        currentPage: page,
        limit: limit,
      };
    } catch (error) {
      logger.error('Failed to get all transactions', error);
      throw new HttpError('Failed to get all transactions', 400);
    }
  },

  getStaffTransactions: async (staffId: any, query: any) => {
    try {
      const page: number = parseInt(query.page as string) || 1;
      const limit: number = parseInt(query.limit as string) || 10;
      const status = query.status;
      const type = query.type;
      const search = query.search as string;
      const startDate = query.startDate as string;
      const endDate = query.endDate as string;

      if (status && !transactionStatus.includes(status.toUpperCase())) {
        throw new HttpError('Invalid transaction status', 400);
      }
      if (type && !transactionType.includes(type.toUpperCase())) {
        throw new HttpError('Invalid transaction type', 400);
      }

      const dateFilter = createDateFilter(startDate, endDate);

      const whereClause: any = {
        OR: [
          {
            fromAccount: {
              staffId: Number(staffId),
            },
          },
          {
            toAccount: {
              staffId: Number(staffId),
            },
          },
        ],
        ...(search
          ? {
              AND: [
                {
                  OR: [
                    { reference: { contains: search, mode: 'insensitive' } },
                    { mode: { contains: search, mode: 'insensitive' } },
                  ],
                },
              ],
            }
          : {}),
        ...(status ? { status: status.toUpperCase() } : {}),
        ...(type ? { type: type.toUpperCase() } : {}),
        ...dateFilter,
      };

      const [transactions, totalPages, totalCount] = await db.$transaction([
        db.transaction.findMany({
          where: whereClause,
          orderBy: { createdAt: 'desc' },
          include: {
            fromAccount: {
              select: {
                staff: {
                  select: {
                    fullName: true,
                    staffCode: true,
                  },
                },
                system: {
                  select: {
                    name: true,
                  },
                },
              },
            },
            toAccount: {
              select: {
                staff: {
                  select: {
                    fullName: true,
                    staffCode: true,
                  },
                },
                system: {
                  select: {
                    name: true,
                  },
                },
              },
            },
          },
          skip: (page - 1) * limit,
          take: limit,
        }),
        db.transaction.count({
          where: whereClause,
        }),
        db.transaction.count({
          where: {
            OR: [
              { fromAccount: { staffId: Number(staffId) } },
              { toAccount: { staffId: Number(staffId) } },
            ],
          },
        }),
      ]);

      return {
        transactions: transactions,
        totalPages: Math.ceil(totalPages / limit),
        totalCount: totalCount,
        currentPage: page,
        limit: limit,
      };
    } catch (error) {
      logger.error('Failed to get staff transactions', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to get staff transactions', 400);
    }
  },

  staffWithdrawal: async (staffId: any, reqBody: any) => {
    try {
      const { amount, details, password } = reqBody;
      const newAmount = new decimal(amount);
      const charges = newAmount.mul(0.05);
      const checkStaff = await db.staff.findUnique({
        where: { id: Number(staffId) },
        include: { account: true },
      });

      if (!checkStaff) {
        throw new HttpError('Staff does not exist', 400);
      }
      if (new decimal(checkStaff.wallet).lessThan(newAmount)) {
        throw new HttpError('Insufficient funds in wallet for withdrawal', 400);
      }

      const existingWithdrawal = await db.transaction.findFirst({
        where: {
          toAccount: { staffId: Number(staffId) },
          type: 'WITHDRAWAL',
          status: {
            in: ['PENDING', 'PROCESSING'],
          },
        },
      });
      if (existingWithdrawal) {
        throw new HttpError(
          'Pending withdrawal request found. Please wait for it to be processed.',
          400
        );
      }

      const verifyPassword = await bcrypt.compare(
        password,
        checkStaff.password || ''
      );
      if (!verifyPassword) {
        throw new HttpError('The password entered is not the correct', 400);
      }

      const companyAccount = await getMainSystemAccount();

      const transaction = await db.transaction.create({
        data: {
          reference: `WID-${timestamp()}${generateCode(4)?.toUpperCase()}`,
          amount: newAmount,
          charges: charges,
          balance: newAmount.minus(charges),
          mode: 'Withdrawal',
          type: 'WITHDRAWAL',
          status: 'PENDING',
          fromAccountId: companyAccount?.account?.id || null,
          toAccountId: checkStaff.account?.id || null,
          remarks: details,
        },
      });

      // Create notification with socket emission
      await notificationService.createNotificationWithSocket(
        Number(staffId),
        'Withdrawal Request Created',
        `Your withdrawal request of ${newAmount} has been submitted and is pending approval.`,
        'transaction',
        'medium',
        {
          transactionId: transaction.id,
          reference: transaction.reference,
          amount: newAmount.toString(),
          type: 'WITHDRAWAL',
        }
      );

      return { message: 'Withdrawal request created successfully' };
    } catch (error) {
      logger.error('Failed to create withdrawal request', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to create withdrawal request', 400);
    }
  },

  staffFundWithdrawalUpdate: async (staffId: any, reqBody: any) => {
    try {
      const auth = createStaffAuthHelper(staffId);
      const { transactionId, status: rawStatus, remarks } = reqBody;
      const status = rawStatus?.toUpperCase();

      const canManage = await auth.hasPermission(PERMISSIONS.TRANSACTION_EDIT);
      if (!canManage) {
        throw new HttpError('Unauthorized', 403);
      }

      const transaction = await db.transaction.findUnique({
        where: { id: transactionId },
        include: {
          toAccount: {
            include: {
              staff: {
                include: {
                  location: true,
                },
              },
            },
          },
        },
      });

      if (!transaction) {
        throw new HttpError('Transaction not found', 404);
      }

      const hasLocationAll = await auth.hasPermission(PERMISSIONS.LOCATION_ALL);
      const hasRegion = await auth.hasPermission(PERMISSIONS.LOCATION_REGION);

      if (!hasLocationAll) {
        if (hasRegion) {
          const regionId = await auth.getRegionId();
          if (transaction.toAccount?.staff?.location?.regionId !== regionId) {
            throw new HttpError('Unauthorized to update this transaction', 403);
          }
        } else {
          const locationId = await auth.getLocationId();
          if (transaction.toAccount?.staff?.locationId !== locationId) {
            throw new HttpError('Unauthorized to update this transaction', 403);
          }
        }
      }

      const currentStatus = transaction.status;

      if (currentStatus === 'PENDING' && status === 'PROCESSING') {
        const currentWallet = new decimal(
          transaction.toAccount?.staff?.wallet || 0
        );

        if (currentWallet.lessThan(transaction.amount)) {
          await db.transaction.update({
            where: { id: transactionId },
            data: {
              status: 'CANCELLED',
              remarks: `${transaction.remarks || ''}\n - Cancelled: Insufficient wallet balance at processing time`,
            },
          });

          // Create notification with socket emission
          await notificationService.createNotificationWithSocket(
            transaction.toAccount?.staff?.id!,
            'Withdrawal Cancelled',
            'Your withdrawal request has been cancelled due to insufficient wallet balance.',
            'transaction',
            'high',
            {
              transactionId,
              status: 'CANCELLED',
              reason: 'Insufficient wallet balance',
            }
          );
        } else {
          await db.$transaction(async (tx) => {
            await tx.transaction.update({
              where: { id: transactionId },
              data: {
                status: 'PROCESSING',
                remarks: `${transaction.remarks || ''}\n - Processing: ${remarks}`,
              },
            });

            await tx.staff.update({
              where: { id: transaction.toAccount?.staff?.id },
              data: {
                wallet: {
                  decrement: transaction.amount,
                },
              },
            });
          });

          // Create notification with socket emission
          await notificationService.createNotificationWithSocket(
            transaction.toAccount?.staff?.id!,
            'Withdrawal Processing',
            `Your withdrawal request of ${transaction.amount} is now being processed.`,
            'transaction',
            'medium',
            {
              transactionId,
              status: 'PROCESSING',
              amount: transaction.amount.toString(),
            }
          );
        }
      } else if (
        currentStatus === 'PROCESSING' &&
        ['FAILED', 'CANCELLED', 'SUCCESS'].includes(status)
      ) {
        if (status !== 'SUCCESS') {
          await db.$transaction(async (tx) => {
            await tx.transaction.update({
              where: { id: transactionId },
              data: {
                status,
                remarks: `${transaction.remarks || ''}\n -${status}: ${remarks}`,
              },
            });

            await tx.staff.update({
              where: { id: transaction.toAccount?.staff?.id },
              data: {
                wallet: {
                  increment: transaction.amount,
                },
              },
            });
          });

          // Emit socket notification
          sendToUser(
            transaction.toAccount?.staff?.id!,
            'withdrawal_status_updated',
            {
              transactionId,
              status,
              amount: transaction.amount.toString(),
              refunded: true,
              timestamp: new Date(),
            }
          );
        } else {
          await db.transaction.update({
            where: { id: transactionId },
            data: {
              status: 'SUCCESS',
              remarks: `${transaction.remarks || ''}\n - Success: ${remarks}`,
            },
          });

          // Create notification with socket emission
          await notificationService.createNotificationWithSocket(
            transaction.toAccount?.staff?.id!,
            'Withdrawal Completed',
            `Your withdrawal of ${transaction.amount} has been completed successfully.`,
            'transaction',
            'medium',
            {
              transactionId,
              amount: transaction.amount.toString(),
              reference: transaction.reference,
              status: 'SUCCESS',
            }
          );
        }
      } else {
        throw new HttpError('Invalid status transition', 400);
      }

      return { message: 'Transaction status updated successfully' };
    } catch (error) {
      logger.error('Failed to update withdrawal request', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to update withdrawal request', 400);
    }
  },

  staffFundTransfer: async (staffId: any, reqBody: any) => {
    try {
      const { amount, toStaffId, password } = reqBody;
      const newAmount = new decimal(amount);
      const checkStaff = await db.staff.findUnique({
        where: { id: Number(staffId) },
        include: { account: true },
      });

      const checReceivingStaff = await db.staff.findUnique({
        where: { staffCode: toStaffId },
        include: { account: true },
      });

      if (!checkStaff) {
        throw new HttpError('Staff does not exist', 400);
      }
      if (!checReceivingStaff) {
        throw new HttpError('receiving staff code appears incorrect', 400);
      }
      if (new decimal(checkStaff.wallet).lessThan(newAmount)) {
        throw new HttpError('Insufficient funds in wallet for transfer', 400);
      }
      const verifyPassword = await bcrypt.compare(
        password,
        checkStaff.password || ''
      );
      if (!verifyPassword) {
        throw new HttpError('The password entered is not the correct', 400);
      }

      const reference = `WID-${timestamp()}${generateCode(4)?.toUpperCase()}`;

      await db.$transaction(async (tx) => {
        await tx.staff.update({
          where: { id: Number(staffId) },
          data: {
            wallet: {
              decrement: newAmount,
            },
          },
        });

        await tx.staff.update({
          where: { staffCode: toStaffId },
          data: {
            wallet: {
              increment: newAmount,
            },
          },
        });
        await tx.transaction.create({
          data: {
            reference,
            amount: newAmount,
            mode: 'internal',
            type: 'TRANSFER',
            status: 'SUCCESS',
            fromAccountId: checkStaff.account?.id || null,
            toAccountId: checReceivingStaff.account?.id || null,
            remarks: 'Staff Internal Fund Transfer',
          },
        });
      });

      // Create notifications with socket emissions for both users
      await notificationService.createNotificationWithSocket(
        Number(staffId),
        'Transfer Sent',
        `You have successfully transferred ${newAmount} to ${checReceivingStaff.fullName}.`,
        'transaction',
        'medium',
        {
          reference,
          amount: newAmount.toString(),
          recipient: checReceivingStaff.fullName,
          recipientCode: toStaffId,
          type: 'TRANSFER_SENT',
        }
      );

      await notificationService.createNotificationWithSocket(
        checReceivingStaff.id,
        'Transfer Received',
        `You have received ${newAmount} from ${checkStaff.fullName}.`,
        'transaction',
        'medium',
        {
          reference,
          amount: newAmount.toString(),
          sender: checkStaff.fullName,
          senderCode: checkStaff.staffCode,
          type: 'TRANSFER_RECEIVED',
        }
      );

      const mailOptions = {
        from: '"Cedarcrest Hospitals Innovations" <<EMAIL>>',
        to: checkStaff.email,
        subject: 'Wallet Transfer Notification',
        template: 'transfer-notification',
        context: {
          sender_name: checkStaff.fullName,
          recipient_name: checReceivingStaff.fullName,
          amount: newAmount,
          reference,
          timestamp: dayjs(new Date()).format('DD MMMM YYYY, h:mm:ss A'),
        },
      };
      enqueueSendEmailJob(mailOptions);
      return { message: 'Transfer successful' };
    } catch (error) {
      logger.error('Failed to transfer funds', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to transfer funds', 400);
    }
  },

  // Send meal voucher to staff
  sendVoucher: async (staffId: number, reqBody: any) => {
    try {
      await staffHasPermission(staffId, PERMISSIONS.TRANSACTION_CREATE);

      const { staffIds, amount, description } = reqBody;

      if (!staffIds || !Array.isArray(staffIds) || staffIds.length === 0) {
        throw new HttpError(
          'Staff IDs are required and must be a non-empty array',
          400
        );
      }

      if (!amount || amount <= 0) {
        throw new HttpError('Amount must be greater than 0', 400);
      }

      const voucherAmount = new decimal(amount);
      const reference = `VOU-${timestamp()}${generateCode(4)?.toUpperCase()}`;

      // Get system account for vouchers
      const systemAccount = await getMainSystemAccount();
      if (!systemAccount || !systemAccount.account) {
        throw new HttpError('System account not found', 500);
      }

      // Get recipient staff details
      const recipients = await db.staff.findMany({
        where: {
          id: { in: staffIds.map((id) => Number(id)) },
          isActive: true,
        },
        select: {
          id: true,
          fullName: true,
          locationId: true,
        },
      });

      if (recipients.length === 0) {
        throw new HttpError('No active staff found with the provided IDs', 404);
      }

      // Check if some staff IDs were not found
      const foundIds = recipients.map((r) => r.id);
      const notFoundIds = staffIds.filter(
        (id) => !foundIds.includes(Number(id))
      );

      if (notFoundIds.length > 0) {
        throw new HttpError(
          `Staff not found or inactive: ${notFoundIds.join(', ')}`,
          404
        );
      }

      // Create transactions and update meal vouchers for all recipients
      const operations = [];

      for (const recipient of recipients) {
        // Update staff meal voucher
        operations.push(
          db.staff.update({
            where: { id: recipient.id },
            data: {
              mealVoucher: {
                increment: voucherAmount,
              },
            },
          })
        );

        // Get or create staff account
        const staffAccount = await db.account.findFirst({
          where: { staffId: recipient.id },
        });

        if (staffAccount) {
          // Create transaction record
          operations.push(
            db.transaction.create({
              data: {
                reference,
                amount: voucherAmount,
                type: 'REWARD',
                status: 'SUCCESS',
                mode: 'Voucher',
                remarks:
                  description || `Meal voucher credit - ${voucherAmount}`,
                fromAccountId: systemAccount.account.id,
                toAccountId: staffAccount.id,
                locationId: recipient.locationId,
              },
            })
          );
        }
      }

      // Execute all operations in a transaction
      await db.$transaction(operations);

      // Send notifications to all recipients
      const recipientIds = recipients.map((r) => r.id);
      if (recipientIds.length > 0) {
        await notificationService.sendBulkNotification(Number(staffId), {
          userIds: recipientIds,
          title: 'Meal Voucher Received',
          message: `You have received a meal voucher of ${voucherAmount}. ${description || ''}`,
          type: 'meal_voucher',
          priority: 'medium',
        });
      }

      return {
        message: `Meal voucher sent successfully to ${recipients.length} recipient(s)`,
        reference,
        amount: voucherAmount.toString(),
        recipientCount: recipients.length,
        recipients: recipients.map((r) => ({
          id: r.id,
          name: r.fullName,
          locationId: r.locationId,
        })),
      };
    } catch (error) {
      logger.error('Failed to send meal voucher', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to send meal voucher', 400);
    }
  },

  // Send meal voucher to all staff in specific locations
  sendLocationVoucher: async (staffId: number, reqBody: any) => {
    try {
      await staffHasPermission(staffId, PERMISSIONS.TRANSACTION_CREATE);

      const { locationIds, amount, description } = reqBody;

      if (
        !locationIds ||
        !Array.isArray(locationIds) ||
        locationIds.length === 0
      ) {
        throw new HttpError(
          'Location IDs are required and must be a non-empty array',
          400
        );
      }

      if (!amount || amount <= 0) {
        throw new HttpError('Amount must be greater than 0', 400);
      }

      const voucherAmount = new decimal(amount);
      const reference = `VOU-${timestamp()}${generateCode(4)?.toUpperCase()}`;

      // Get system account for vouchers
      const systemAccount = await getMainSystemAccount();
      if (!systemAccount || !systemAccount.account) {
        throw new HttpError('System account not found', 500);
      }

      // Verify locations exist
      const locations = await db.location.findMany({
        where: {
          id: { in: locationIds.map((id) => Number(id)) },
        },
        select: {
          id: true,
          name: true,
        },
      });

      if (locations.length === 0) {
        throw new HttpError(
          'No valid locations found with the provided IDs',
          404
        );
      }

      const foundLocationIds = locations.map((n) => n.id);
      const notFoundLocationIds = locationIds.filter(
        (id) => !foundLocationIds.includes(Number(id))
      );

      if (notFoundLocationIds.length > 0) {
        throw new HttpError(
          `Locations not found: ${notFoundLocationIds.join(', ')}`,
          404
        );
      }

      // Get all active staff in the specified locations
      const recipients = await db.staff.findMany({
        where: {
          locationId: { in: foundLocationIds },
          isActive: true,
        },
        select: {
          id: true,
          fullName: true,
          locationId: true,
          location: {
            select: {
              name: true,
            },
          },
        },
      });

      if (recipients.length === 0) {
        throw new HttpError(
          'No active staff found in the specified locations',
          404
        );
      }

      // Create transactions and update meal vouchers for all recipients
      const operations = [];

      for (const recipient of recipients) {
        // Update staff meal voucher
        operations.push(
          db.staff.update({
            where: { id: recipient.id },
            data: {
              mealVoucher: {
                increment: voucherAmount,
              },
            },
          })
        );

        // Get or create staff account
        const staffAccount = await db.account.findFirst({
          where: { staffId: recipient.id },
        });

        if (staffAccount) {
          // Create transaction record
          operations.push(
            db.transaction.create({
              data: {
                reference,
                amount: voucherAmount,
                type: 'REWARD',
                status: 'SUCCESS',
                mode: 'Voucher',
                remarks:
                  description ||
                  `Location-based meal voucher credit - ${voucherAmount}`,
                fromAccountId: systemAccount.account.id,
                toAccountId: staffAccount.id,
                locationId: recipient.locationId,
              },
            })
          );
        }
      }

      // Execute all operations in a transaction
      await db.$transaction(operations);

      // Send notifications to all recipients
      const recipientIds = recipients.map((r) => r.id);
      if (recipientIds.length > 0) {
        await notificationService.sendBulkNotification(Number(staffId), {
          userIds: recipientIds,
          title: 'All Staff Meal Voucher Received',
          message: `You have received a location-based meal voucher of ${voucherAmount}. ${description || ''}`,
          type: 'meal_voucher',
          priority: 'medium',
        });
      }

      return {
        message: `Meal voucher sent successfully to ${recipients.length} staff across ${locations.length} location(s)`,
      };
    } catch (error) {
      logger.error('Failed to send location meal voucher', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to send location meal voucher', 400);
    }
  },
};
