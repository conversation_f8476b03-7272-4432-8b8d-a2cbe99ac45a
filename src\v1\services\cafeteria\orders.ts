import { db, decimal } from '../../utils/model';
import { HttpError } from '../../utils/httpError';
import {
  createDateFilter,
  createCommonDateFilter,
  timestamp,
  generateCode,
} from '../../utils/util';
import { OrderType, PaymentType, SaleType } from '../../utils/util';
import dayjs from 'dayjs';
import {
  staffHasPermission,
  PERMISSIONS,
  createStaffAuthHelper,
} from '../../utils/permission';
import { formatString } from '../../utils/stringFormatter';
import { createCSVExport, CSV_CONFIGS } from '../../utils/csvExport';
import { Prisma } from '@prisma/client';
import { notificationService } from '../notification';
import { getCafeteriaSystemAccount, getMainSystemAccount } from '../system';
import { logger } from '../../utils/logger';
import { numberFormat } from '../../utils/util';


export const orderService = {
  checkStaffPayment: async (staffId: any, reqBody: any) => {
    try {
      const auth = createStaffAuthHelper(staffId);
      console.log('Auth helper created.');

      const canManage = await auth.hasPermission(
        PERMISSIONS.CAFETERIA_POS_ACCESS
      );

      if (!canManage) {
        throw new HttpError('Unauthorized', 403);
      }

      const { code, amount } = reqBody;
      const staffCode = formatString.trimString(code);

      const checkStaff = await db.staff.findUnique({
        where: { staffCode },
        include: { location: true, account: true },
      });

      if (!checkStaff || !checkStaff.isActive) {
        throw new HttpError('Staff does not exist', 400);
      }
      if (checkStaff.locked) {
        throw new HttpError('Staff account is locked', 400);
      }

      const requesterLocationId = await auth.getLocationId();

      const requesterLocation = await db.location.findUnique({
        where: { id: Number(requesterLocationId) },
      });

      const sameGroup =
        requesterLocation?.regionId === checkStaff.location.regionId;

      if (!sameGroup) {
        throw new HttpError(
          'You are not authorized to access this cafeteria',
          403
        );
      }

      const { creditLimit, wallet, monthlyCreditUsed, mealVoucher } =
        checkStaff;
      const decimalAmount = new decimal(amount);

      const isCreditSufficient = creditLimit
        .sub(monthlyCreditUsed)
        .gte(decimalAmount);
      const isWalletSufficient = new decimal(wallet).gte(decimalAmount);
      const isMealVoucherSufficient = mealVoucher.gte(decimalAmount);

      return {
        creditLimit: isCreditSufficient,
        wallet: isWalletSufficient,
        mealVoucher: isMealVoucherSufficient,
        name: checkStaff.fullName,
      };
    } catch (error) {
      logger.error('Error checking staff payment:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to check staff payment', 500);
    }
  },

  createOrder: async (staffId: any, reqBody: any) => {
    try {
      const { cart, code, type, saleType, method, totalAmount, ...order } =
        reqBody;
      const auth = createStaffAuthHelper(staffId);
      const amount = new decimal(totalAmount);

      const canManage = await auth.hasPermission(
        PERMISSIONS.CAFETERIA_POS_ACCESS
      );
      if (!canManage) {
        throw new HttpError('Unauthorized', 403);
      }

      const requesterLocationId = await auth.getLocationId();

      const requesterLocation = await db.location.findUnique({
        where: { id: Number(requesterLocationId) },
      });

      if (!requesterLocation) {
        throw new HttpError('Invalid location', 400);
      }

      if (!PaymentType.includes(method.toUpperCase())) {
        throw new HttpError('Not an acceptable payment method', 403);
      }

      if (!OrderType.includes(type.toUpperCase())) {
        throw new HttpError('Not an acceptable order type', 403);
      }

      if (!SaleType.includes(saleType.toUpperCase())) {
        throw new HttpError('Not an acceptable sale type', 403);
      }

      const cafeteriaSystemAccount = await getCafeteriaSystemAccount();

      const orderNumber = `ORD-${timestamp()}${generateCode(4)?.toUpperCase()}`;
      const operations: any[] = [];

      let staffCode = null;
      let staffRecord = null;

      if (code) {
        staffCode = formatString.trimString(code);

        staffRecord = await db.staff.findUnique({
          where: { staffCode },
          include: { location: true, account: true },
        });

        if (!staffRecord || !staffRecord.isActive) {
          throw new HttpError('Staff does not exist', 400);
        }

        const sameGroup =
          requesterLocation.regionId === staffRecord.location.regionId;
        if (!sameGroup) {
          throw new HttpError(
            'You are not authorized to access this cafeteria',
            403
          );
        }

        const methodUpper = method.toUpperCase();


        if (methodUpper === 'CREDIT') {
          operations.push(
            db.staff.update({
              where: { staffCode },
              data: {
                monthlyCreditUsed: {
                  increment: amount,
                },
              },
            })
          );
        }

        if (methodUpper === 'WALLET') {
          operations.push(
            db.staff.update({
              where: { staffCode },
              data: {
                wallet: {
                  decrement: amount,
                },
              },
            })
          );
        }

        if (methodUpper === 'VOUCHER') {
          operations.push(
            db.staff.update({
              where: { staffCode },
              data: {
                mealVoucher: {
                  decrement: amount,
                },
              },
            })
          );
        }
      }

      const orderCreation = db.cafeteriaOrder.create({
        data: {
          ...order,
          orderNumber,
          totalAmount: amount,
          paymentType: method.toUpperCase(),
          orderType: type.toUpperCase(),
          saleType: saleType.toUpperCase(),
          locationId: Number(requesterLocationId),
          staffId: staffRecord?.id || null,
          orderItems: {
            create: cart.map((item: any) => ({
              menuItemId: Number(item.id),
              quantity: Number(item.quantity),
              unitPrice: new decimal(item.price),
              totalPrice: new decimal(
                Number(item.quantity) * Number(item.price)
              ),
            })),
          },
        },
      });

      operations.push(orderCreation);

      const transactionCreation = db.transaction.create({
        data: {
          amount: amount,
          status: 'SUCCESS',
          type: 'CAFETERIA',
          locationId: Number(requesterLocationId),
          mode: method.toLowerCase(),
          reference: orderNumber,
          remarks: `Cafeteria order - ${orderNumber}`,
          fromAccountId: staffRecord?.account?.id,
          toAccountId: cafeteriaSystemAccount?.account?.id,
        },
      });
      operations.push(transactionCreation);

      // if (staffRecord && staffRecord.account) {
      //   const staffUpdate = db.staff.update({
      //     where: { id: staffRecord.id },
      //     data: {
      //       locked: true,
      //     },
      //   });
      //   operations.push(staffUpdate);
      // }

      // Execute all operations transactionally
      const results = await db.$transaction(operations);

      // Notify staff who placed the order
      if (staffRecord) {
        await notificationService.createNotificationWithSocket(
          staffRecord.id,
          'Order Created',
          `Your cafeteria order ${orderNumber} has been placed successfully.`,
          'order',
          'medium',
          {
            orderNumber,
            totalAmount: amount.toString(),
            paymentType: method.toUpperCase(),
            orderType: type.toUpperCase(),
          }
        );
      }

      // Find the order creation result from the operations
      const orderResult = results.find((result) => result.orderNumber);
      const orderId = orderResult?.id;

      return {
        orderId: orderId,
        message: 'Order created successfully',
      };
    } catch (error) {
      logger.error('Error creating order:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      // throw new HttpError('Failed to create order', 400);
    }
  },

  getOrderById: async (staffId: any, id: string) => {
    try {
      await staffHasPermission(staffId, PERMISSIONS.CAFETERIA_ORDERS_MANAGE);
      const order = await db.cafeteriaOrder.findUnique({
        where: { id },
        include: {
          orderItems: {
            include: {
              menuItem: true,
            },
          },
          staff: true,
        },
      });
      if (!order) {
        throw new HttpError('Order not found', 404);
      }
      return order;
    } catch (error) {
      logger.error('Error getting order by id:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to fetch order', 400);
    }
  },

  getStaffOrders: async (staffId: any, query: any = {}) => {
    try {
      const auth = createStaffAuthHelper(staffId);

      const locationId = await auth.getLocationId();

      const page: number = parseInt(query.page as string);
      const limit: number = parseInt(query.limit as string);
      const search: string = (query.search as string) || '';
      const paymentType = query.paymentType as string;

      const startDate = query.startDate as string;
      const endDate = query.endDate as string;
      const exportFormat = query.export as string;
      const dateFilter = createDateFilter(startDate, endDate);

      // Create date filter for stats - prioritize startDate/endDate over month/year
      let statsDateFilter = {};
      let selectedYear, selectedMonth;

      if (startDate || endDate) {
        statsDateFilter = createDateFilter(startDate, endDate);
      } else {
        selectedYear = query?.year;
        selectedMonth = query?.month;
        const currentDate = new Date();
        const currentYear = currentDate.getFullYear();
        const currentMonth = currentDate.getMonth() + 1;

        if (!selectedYear || isNaN(Number(selectedYear))) {
          selectedYear = currentYear;
        } else {
          selectedYear = Number(selectedYear);
        }

        if (
          !selectedMonth ||
          isNaN(Number(selectedMonth)) ||
          Number(selectedMonth) < 1 ||
          Number(selectedMonth) > 12
        ) {
          selectedMonth = currentMonth;
        } else {
          selectedMonth = Number(selectedMonth);
        }

        const monthStart = dayjs(`${selectedYear}-${selectedMonth}-01`)
          .startOf('month')
          .toDate();
        const monthEnd = dayjs(`${selectedYear}-${selectedMonth}-01`)
          .endOf('month')
          .toDate();

        statsDateFilter = {
          createdAt: {
            gte: monthStart,
            lte: monthEnd,
          },
        };
      }

      const matchingPaymentTypes = Object.values(PaymentType).filter((type) =>
        type.toLowerCase().includes(search.toLowerCase())
      );

      const searchFilter = search
        ? {
            OR: [
              {
                patientRoomNo: {
                  contains: search,
                  mode: Prisma.QueryMode.insensitive,
                },
              },
              {
                customerName: {
                  contains: search,
                  mode: Prisma.QueryMode.insensitive,
                },
              },
              {
                customerPhone: {
                  contains: search,
                  mode: Prisma.QueryMode.insensitive,
                },
              },
              ...(matchingPaymentTypes.length > 0
                ? [{ paymentType: { in: matchingPaymentTypes as any } }]
                : []),
            ],
          }
        : {};

      const overallWhereClause = {
        staffId: Number(staffId),
        ...(locationId !== undefined && locationId !== null
          ? { locationId }
          : {}),
      };

      const baseWhereClause = {
        staffId: Number(staffId),
        ...(paymentType
          ? { paymentType: paymentType.toUpperCase() as any }
          : {}),
        ...(locationId !== undefined && locationId !== null
          ? { locationId }
          : {}),
        ...searchFilter,
      };

      const whereClause = {
        ...baseWhereClause,
        ...(startDate || endDate
          ? dateFilter
          : query.month || query.year
            ? statsDateFilter
            : statsDateFilter),
      };

      // Handle CSV export
      if (exportFormat === 'csv') {
        const orders = await db.cafeteriaOrder.findMany({
          where: whereClause,
          orderBy: { createdAt: 'desc' },
          include: {
            orderItems: {
              include: {
                menuItem: {
                  select: {
                    name: true,
                  },
                },
              },
            },
            staff: {
              select: {
                fullName: true,
                staffCode: true,
              },
            },
          },
        });

        const flattenedOrders = orders.map((order: any) => {
          const baseOrder = {
            orderNumber: order.orderNumber,
            staffName: order.staff?.fullName || 'N/A',
            staffIdNo: order.staff?.staffCode || 'N/A',
            orderType: order.orderType,
            paymentType: order.paymentType,
            totalAmount: Number(order.totalAmount),
            date: order.createdAt.toLocaleString(),
            items: order.orderItems
              .map(
                (item: any) =>
                  `${item.menuItem.name} (${item.quantity}x${Number(item.unitPrice)})`
              )
              .join('; '),
            itemsCount: order.orderItems.length,
          };

          // Add creditPaid column only for CREDIT payment type
          if (order.paymentType === 'CREDIT') {
            return {
              ...baseOrder,
              creditPaid: order.creditPaid ? 'YES' : 'NO',
            };
          }

          return baseOrder;
        });

        // Create dynamic headers based on whether any orders have CREDIT payment type
        const hasCreditOrders = orders.some(order => order.paymentType === 'CREDIT');
        const baseHeaders = [
          'orderNumber',
          'staffName',
          'staffIdNo',
          'orderType',
          'paymentType',
          'totalAmount',
          'date',
          'items',
          'itemsCount',
        ];

        const headers = hasCreditOrders
          ? [...baseHeaders.slice(0, 5), 'creditPaid', ...baseHeaders.slice(5)]
          : baseHeaders;

        return createCSVExport(flattenedOrders, 'my_orders', {
          ...CSV_CONFIGS.orders,
          headers,
        });
      }

      const [orders, totalCount, overallOrders, totalAmount, unpaidCreditAmount] =
        await db.$transaction([
          db.cafeteriaOrder.findMany({
            where: whereClause,
            orderBy: { createdAt: 'desc' },
            skip: (page - 1) * limit,
            take: limit,
            include: {
              orderItems: {
                include: {
                  menuItem: true,
                },
              },
            },
          }),
          db.cafeteriaOrder.count({
            where: whereClause,
          }),
          db.cafeteriaOrder.count({
            where: overallWhereClause,
          }),
          db.cafeteriaOrder.aggregate({
            _sum: {
              totalAmount: true,
            },
            where: whereClause,
          }),
          db.cafeteriaOrder.aggregate({
            _sum: {
              totalAmount: true,
            },
            where: {
              ...whereClause,
              paymentType: 'CREDIT',
              creditPaid: false,
            },
          }),
        ]);

      const response: any = {
        orders: orders,
        totalPages: Math.ceil(totalCount / limit),
        totalCount: totalCount,
        currentPage: page,
        limit: limit,
        overallOrders,
        totalOrders: totalCount,
        totalAmount: totalAmount._sum?.totalAmount || 0,
        unpaidCreditAmount: unpaidCreditAmount._sum?.totalAmount || 0,
      };

      return response;
    } catch (error) {
      logger.error('Error getting staff orders:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to fetch staff orders', 400);
    }
  },

  getAllOrders: async (staffId: any, query: any = {}) => {
    try {
      const auth = createStaffAuthHelper(staffId);

      const canManage = await auth.hasPermission(
        PERMISSIONS.CAFETERIA_ORDERS_MANAGE
      );
      if (!canManage) {
        throw new HttpError('Unauthorized', 403);
      }

      const hasLocationAll = await auth.hasPermission(PERMISSIONS.LOCATION_ALL);
      const locationId = hasLocationAll
        ? undefined
        : await auth.getLocationId();

      const page: number = parseInt(query.page as string) || 1;
      const limit: number = parseInt(query.limit as string) || 10;
      const search: string = (query.search as string) || '';
      const paymentType = query.paymentType as string;
      const saleType = query.saleType as string;
      const startDate = query.startDate as string;
      const endDate = query.endDate as string;
      const presetDate = query.presetDate as string;
      const staff = query.staff;
      const exportFormat = query.export as string;
      const dateFilter = createDateFilter(startDate, endDate);
      const daysFilter = createCommonDateFilter(presetDate as any);

      // Create stats date filter
      let selectedYear = query?.year;
      let selectedMonth = query?.month;
      const currentDate = new Date();
      const currentYear = currentDate.getFullYear();
      const currentMonth = currentDate.getMonth() + 1;

      let statsDateFilter = {};
      if (startDate || endDate) {
        statsDateFilter = createDateFilter(startDate, endDate);
      } else if (selectedYear || selectedMonth) {
        if (!selectedYear || isNaN(Number(selectedYear))) {
          selectedYear = currentYear;
        } else {
          selectedYear = Number(selectedYear);
        }

        if (
          !selectedMonth ||
          isNaN(Number(selectedMonth)) ||
          Number(selectedMonth) < 1 ||
          Number(selectedMonth) > 12
        ) {
          selectedMonth = currentMonth;
        } else {
          selectedMonth = Number(selectedMonth);
        }

        const monthStart = dayjs(`${selectedYear}-${selectedMonth}-01`)
          .startOf('month')
          .toDate();
        const monthEnd = dayjs(`${selectedYear}-${selectedMonth}-01`)
          .endOf('month')
          .toDate();

        statsDateFilter = {
          createdAt: {
            gte: monthStart,
            lte: monthEnd,
          },
        };
      }

      const todayStart = dayjs().startOf('day').toDate();
      const todayEnd = dayjs().endOf('day').toDate();

      const matchingPaymentTypes = Object.values(PaymentType).filter((type) =>
        type.toLowerCase().includes(search.toLowerCase())
      );

      const searchFilter = search
        ? {
            OR: [
              {
                patientRoomNo: {
                  contains: search,
                  mode: Prisma.QueryMode.insensitive,
                },
              },
              {
                customerName: {
                  contains: search,
                  mode: Prisma.QueryMode.insensitive,
                },
              },
              {
                customerPhone: {
                  contains: search,
                  mode: Prisma.QueryMode.insensitive,
                },
              },
              ...(matchingPaymentTypes.length > 0
                ? [{ paymentType: { in: matchingPaymentTypes as any } }]
                : []),
              {
                staff: {
                  OR: [
                    {
                      fullName: {
                        contains: search,
                        mode: Prisma.QueryMode.insensitive,
                      },
                    },
                    {
                      staffCode: {
                        contains: search,
                        mode: Prisma.QueryMode.insensitive,
                      },
                    },
                  ],
                },
              },
            ],
          }
        : {};

      const overallWhereClause = {
        ...(staff ? { staffId: Number(staff) } : {}),
        ...(locationId !== undefined && locationId !== null
          ? { locationId }
          : {}),
      };

      const baseWhereClause = {
        ...(paymentType
          ? { paymentType: paymentType.toUpperCase() as any }
          : {}),
        ...(saleType ? { saleType: saleType.toUpperCase() as any } : {}),
        ...(staff ? { staffId: Number(staff) } : {}),
        ...(locationId !== undefined && locationId !== null
          ? { locationId }
          : {}),
        ...searchFilter,
      };

      const defaultDailyFilter = {
        createdAt: {
          gte: todayStart,
          lte: todayEnd,
        },
      };

      const whereClause = {
        ...baseWhereClause,
        ...(startDate || endDate
          ? dateFilter
          : presetDate
            ? daysFilter
            : query.month || query.year
              ? statsDateFilter
              : defaultDailyFilter),
      };

      // Handle CSV export
      if (exportFormat === 'csv') {
        const orders = await db.cafeteriaOrder.findMany({
          where: whereClause,
          orderBy: { createdAt: 'desc' },
          include: {
            orderItems: {
              include: {
                menuItem: {
                  select: {
                    name: true,
                  },
                },
              },
            },
            staff: {
              select: {
                id: true,
                fullName: true,
                staffCode: true,
              },
            },
          },
        });

        const flattenedOrders = orders.map((order: any) => {
          const baseOrder = {
            orderNumber: order.orderNumber,
            staffName: order.staff?.fullName || 'N/A',
            staffIdNo: order.staff?.staffCode || 'N/A',
            // customerName: order.customerName || 'N/A',
            // customerPhone: order.customerPhone || 'N/A',
            // patientRoomNo: order.patientRoomNo || 'N/A',
            orderType: order.orderType,
            paymentType: order.paymentType,
            totalAmount: Number(order.totalAmount),
            date: order.createdAt.toLocaleString(),
            items: order.orderItems
              .map(
                (item: any) =>
                  `${item.menuItem.name} (${item.quantity}x${Number(item.unitPrice)})`
              )
              .join('; '),
            itemsCount: order.orderItems.length,
          };

          // Add creditPaid column only for CREDIT payment type
          if (order.paymentType === 'CREDIT') {
            return {
              ...baseOrder,
              creditPaid: order.creditPaid ? 'YES' : 'NO',
            };
          }

          return baseOrder;
        });

        // Create dynamic headers based on whether any orders have CREDIT payment type
        const hasCreditOrders = orders.some(order => order.paymentType === 'CREDIT');
        const baseHeaders = [
          'orderNumber',
          'staffName',
          'staffIdNo',
          'orderType',
          'paymentType',
          'totalAmount',
          'date',
          'items',
          'itemsCount',
        ];

        const headers = hasCreditOrders
          ? [...baseHeaders.slice(0, 5), 'creditPaid', ...baseHeaders.slice(5)]
          : baseHeaders;

        return createCSVExport(flattenedOrders, 'orders', {
          ...CSV_CONFIGS.orders,
          headers,
        });
      }

      const [orders, totalCount, overallOrders, totalAmount, itemAggregation, unpaidCreditAmount] =
        await db.$transaction([
          db.cafeteriaOrder.findMany({
            where: whereClause,
            orderBy: { createdAt: 'desc' },
            skip: (page - 1) * limit,
            take: limit,
            include: {
              orderItems: {
                include: {
                  menuItem: true,
                },
              },
              staff: {
                select: {
                  id: true,
                  fullName: true,
                  staffCode: true,
                },
              },
            },
          }),
          db.cafeteriaOrder.count({
            where: whereClause,
          }),
          db.cafeteriaOrder.count({
            where: overallWhereClause,
          }),
          db.cafeteriaOrder.aggregate({
            _sum: {
              totalAmount: true,
            },
            where: whereClause,
          }),
          db.cafeteriaOrderItem.groupBy({
            by: ['menuItemId'],
            where: {
              orderId: {
                in: (
                  await db.cafeteriaOrder.findMany({
                    where: whereClause,
                    select: { id: true },
                  })
                ).map((o) => o.id),
              },
            },
            _sum: {
              quantity: true,
              totalPrice: true,
            },
            orderBy: {
              menuItemId: 'asc',
            },
          }),
          db.cafeteriaOrder.aggregate({
            _sum: {
              totalAmount: true,
            },
            where: {
              ...whereClause,
              paymentType: 'CREDIT',
              creditPaid: false,
            },
          }),
        ]);

      // Process item aggregation
      const menuItems = await db.cafeteriaMenu.findMany({
        where: {
          id: {
            in: itemAggregation.map((item) => item.menuItemId),
          },
        },
        select: {
          id: true,
          name: true,
        },
      });

      const aggregatedItems = itemAggregation.map((item) => {
        const menuItem = menuItems.find((m: any) => m.id === item.menuItemId);
        return {
          menuName: menuItem?.name || 'Unknown',
          totalQuantity: item._sum?.quantity || 0,
          totalPrice: item._sum?.totalPrice || 0,
        };
      });

      const response: any = {
        orders: orders,
        totalPages: Math.ceil(totalCount / limit),
        totalCount: totalCount,
        currentPage: page,
        limit: limit,
        overallOrders,
        totalOrders: totalCount,
        totalAmount: totalAmount._sum?.totalAmount || 0,
        unpaidCreditAmount: unpaidCreditAmount._sum?.totalAmount || 0,
        aggregatedItems,
      };

      return response;
    } catch (error) {
      logger.error('Error getting all orders:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to fetch all orders', 400);
    }
  },

  printReceipt: async (staffId: any, orderId: string) => {
    try {
      const auth = createStaffAuthHelper(staffId);
      const canManage = await auth.hasPermission(
        PERMISSIONS.CAFETERIA_POS_ACCESS
      );
      if (!canManage) {
        throw new HttpError('Unauthorized', 403);
      }

      const order = await db.cafeteriaOrder.findUnique({
        where: { id: orderId },
        include: {
          orderItems: {
            include: {
              menuItem: true,
            },
          },
          staff: {
            select: {
              fullName: true,
              staffCode: true,
            },
          },
        },
      });

      if (!order) {
        throw new HttpError('Order not found', 404);
      }

      // Format receipt with minimal paper waste
      const receiptHtml = `
        <html>
          <head>
            <style>
              @page {
                size: 80mm auto;
                margin: 0;
              }
              @media print {
                html, body {
                  height: auto !important;
                  min-height: 0 !important;
                }
                body {
                  -webkit-print-color-adjust: exact;
                  print-color-adjust: exact;
                }
              }
              * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
              }
              html, body {
                height: auto;
                min-height: 0;
                overflow: hidden;
              }
              body { 
                font-family: monospace; 
                width: 80mm;
                font-size: 12px;
                line-height: 1.1;
                padding: 2mm;
              }
              .center { text-align: center; }
              .line { border-bottom: 1px dashed #000; margin: 1mm 0; }
              h3 { margin: 1mm 0; font-size: 18px; font-weight: bold }
              p { margin: 0.5mm 0; }
              .right { text-align: right; }
              .receipt-end { page-break-after: always; }
              table {
                width: 100%;
                border-collapse: collapse;
                margin: 3mm 0;
              }
              td, th {
                padding: 1mm 2mm;
                vertical-align: top;
              }
              th {
                font-weight: bold;
                text-align: left;
              }
              .item-name { width: 40%; }
              .item-qty { width: 15%; text-align: center; }
              .item-rate { width: 20%; text-align: right; }
              .item-amount { width: 25%; text-align: right; }
            </style>
          </head>
          <body>
            <div class="center">
              <h3>CHL CAFETERIA RECEIPT</h3>
            </div>
            <div class="line"></div>
            <p>Order: ${order.orderNumber}</p>
            <p>Date: ${order.createdAt.toLocaleString()}</p>
            ${order.staff?.fullName ? `<p>Staff: ${order.staff.fullName}</p>` : ''}
            ${order.staff?.staffCode ? `<p>ID: ${order.staff.staffCode}</p>` : ''}
            ${order.customerName ? `<p>Customer: ${order.customerName}</p>` : ''}
            ${order.customerPhone ? `<p>Phone: ${order.customerPhone}</p>` : ''}
            ${order.patientRoomNo ? `<p>Room: ${order.patientRoomNo}</p>` : ''}
            <div class="line"></div>
            <table>
              <tr>
                <th class="item-name">ITEMS</th>
                <th class="item-qty">QTY</th>
                <th class="item-rate">RATE(₦)</th>
                <th class="item-amount">AMOUNT(₦)</th>
              </tr>
              ${order.orderItems
                .map(
                  (item) => `
                <tr>
                  <td class="item-name">${item.menuItem.name}</td>
                  <td class="item-qty">${item.quantity}</td>
                  <td class="item-rate">${item.unitPrice}</td>
                  <td class="item-amount">${item.totalPrice}</td>
                </tr>
              `
                )
                .join('')}
            </table>
            <div class="line"></div>
            <p class="right"><strong>TOTAL: ${numberFormat(order.totalAmount)}</strong></p>
            ${order.paymentType ? `<p class="right"><strong>PAYMENT: ${order.paymentType}</strong></p>` : ''}
            <div class="line"></div>
            <p class="center">Thank you for your order!</p>
            <br>
            <br>
            <br />
            <br />
            <div class="receipt-end"></div>
          </body>
        </html>
      `;

      return {
        success: true,
        message: 'Receipt ready for printing',
        receiptHtml,
      };
    } catch (error) {
      logger.error('Error generating receipt:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to generate receipt', 500);
    }
  },
};

export const specialOrderService = {
  createSpecialOrder: async (staffId: any, reqBody: any) => {
    try {
      const auth = createStaffAuthHelper(staffId);

      const locationId = await auth.getLocationId();

      const { cart, ...order } = reqBody;

      const existingMenu = await db.cafeteriaSpecialOrder.findFirst({
        where: {
          staffId: Number(staffId),
          status: 'PENDING',
        },
      });

      if (existingMenu) {
        throw new HttpError('You have a pending special order request', 400);
      }
      const orderNumber = `SPORD-${timestamp()}${generateCode(3)?.toUpperCase()}`;

      const specialOrder = await db.cafeteriaSpecialOrder.create({
        data: {
          ...order,
          orderNumber: orderNumber,
          staffId: Number(staffId),
          locationId: Number(locationId),
          orderItems: {
            create: cart.map((item: any) => ({
              menuItemId: Number(item.id),
              quantity: Number(item.quantity),
              unitPrice: Number(item.price),
              totalPrice: Number(item.quantity) * Number(item.price),
            })),
          },
        },
        include: {
          staff: {
            select: {
              fullName: true,
            },
          },
        },
      });

      // Get staff with CAFETERIA_SPECIAL_APPROVE permission
      const staffWithApprovalPermission = await db.staff.findMany({
        where: {
          isActive: true,
          roles: {
            some: {
              permissions: {
                some: {
                  action: PERMISSIONS.CAFETERIA_SPECIAL_APPROVE,
                },
              },
            },
          },
        },
        select: {
          id: true,
          fullName: true,
        },
      });

      // Send notifications to all staff with approval permission
      if (staffWithApprovalPermission.length > 0) {
        const staffIds = staffWithApprovalPermission.map((staff) => staff.id);

        // Send notifications directly without permission check
        const notifications = staffIds.map((id) => ({
          staffId: id,
          title: 'New Special Order Request',
          message: `${specialOrder.staff.fullName} has submitted a special order request (${orderNumber}) that requires approval.`,
          type: 'cafeteria_special_order',
          priority: 'medium',
        }));

        await db.notification.createMany({
          data: notifications,
        });
      }

      return { message: 'Special Menu item created successfully' };
    } catch (error) {
      logger.error('Error creating special order:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to create special order', 400);
    }
  },
  getAllSpecialOrders: async (staffId: any, query: any = {}) => {
    try {
      const auth = createStaffAuthHelper(staffId);

      const hasLocationAll = await auth.hasPermission(PERMISSIONS.LOCATION_ALL);
      const canManageOrders = await auth.hasPermission(
        PERMISSIONS.CAFETERIA_ORDERS_MANAGE
      );

      const page: number = parseInt(query.page as string);
      const limit: number = parseInt(query.limit as string);
      const search: string = (query.search as string) || '';
      const startDate = query.startDate as string;
      const endDate = query.endDate as string;
      const dateFilter = createDateFilter(startDate, endDate);

      let whereClause: any = {
        ...(search
          ? {
              OR: [
                { orderNumber: { contains: search, mode: 'insensitive' } },
                { purpose: { contains: search, mode: 'insensitive' } },
              ],
            }
          : {}),
        ...dateFilter,
      };

      if (hasLocationAll) {
      } else if (canManageOrders) {
        const locationId = await auth.getLocationId();
        const location = await db.location.findUnique({
          where: { id: Number(locationId) },
          select: { regionId: true },
        });

        whereClause.location = {
          regionId: location?.regionId,
        };
      } else {
        // Load orders by locationId only
        const locationId = await auth.getLocationId();
        whereClause.locationId = Number(locationId);
      }

      const [orders, totalCount] = await db.$transaction([
        db.cafeteriaSpecialOrder.findMany({
          where: whereClause,
          orderBy: { createdAt: 'desc' },
          skip: (page - 1) * limit,
          take: limit,
          include: {
            orderItems: {
              include: {
                menuItem: true,
              },
            },
            returnedItems: {
              include: {
                menuItem: true,
              },
            },
            staff: {
              select: {
                fullName: true,
              },
            },
          },
        }),
        db.cafeteriaSpecialOrder.count({
          where: whereClause,
        }),
      ]);

      const response = {
        specialOrders: orders,
        totalPages: Math.ceil(totalCount / limit),
        totalCount: totalCount,
        currentPage: page,
        limit: limit,
      };

      return response;
    } catch (error) {
      logger.error('Error getting all special orders:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to fetch special orders', 400);
    }
  },

  updateSpecialOrder: async (staffId: any, reqBody: any) => {
    try {
      const auth = createStaffAuthHelper(staffId);

      const canApprove = await auth.hasPermission(
        PERMISSIONS.CAFETERIA_SPECIAL_APPROVE
      );

      const canManage = await auth.hasPermission(
        PERMISSIONS.CAFETERIA_ORDERS_MANAGE
      );

      const {
        orderId,
        comment,
        approvedBy,
        declinedBy,
        deliveredBy,
        status: requestedStatus,
      } = reqBody;

      const order = await db.cafeteriaSpecialOrder.findUnique({
        where: { id: orderId },
      });

      if (!order) {
        throw new HttpError('Order not found', 404);
      }

      const currentStatus = order.status;

      // Handle PENDING status
      if (currentStatus === 'PENDING') {
        if (!['APPROVED', 'DECLINED'].includes(requestedStatus)) {
          throw new HttpError('Invalid status transition from PENDING', 400);
        }
        if (!canApprove) {
          throw new HttpError('Unauthorized to approve or decline', 403);
        }
      }
      // Handle APPROVED status
      else if (currentStatus === 'APPROVED') {
        if (requestedStatus !== 'DELIVERED') {
          throw new HttpError('Invalid status transition from APPROVED', 400);
        }
        if (!canManage) {
          throw new HttpError('Unauthorized to mark as delivered', 403);
        }
      } else if (currentStatus === 'DELIVERED') {
        if (requestedStatus !== 'RECEIVED') {
          throw new HttpError('Invalid status transition from DELIVERED', 400);
        }
        if (order.staffId !== Number(staffId)) {
          throw new HttpError(
            'Only the ordering staff can receive this order',
            403
          );
        }
      } else {
        throw new HttpError('This order status cannot be changed', 400);
      }

      await db.cafeteriaSpecialOrder.update({
        where: { id: orderId },
        data: {
          approvedBy,
          declinedBy,
          deliveredBy,
          status: requestedStatus,
          ...(comment && { comment }),
        },
      });

      await notificationService.createNotificationWithSocket(
        order.staffId,
        'Special Order Updated',
        `Your special order ${order.orderNumber} has been ${requestedStatus} by ${approvedBy || declinedBy || deliveredBy}.`,
        'order',
        'medium',
        {
          orderNumber: order.orderNumber,
          status: requestedStatus,
          comment,
        }
      );

      return { message: 'Order status updated successfully' };
    } catch (error) {
      logger.error('Error updating special order:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to update special order', 400);
    }
  },

  receiveSpecialOrder: async (staffId: any, reqBody: any) => {
    try {
      const { orderId, receivedBy, totalAmount, receivedItems } = reqBody;

      const order = await db.cafeteriaSpecialOrder.findUnique({
        where: { id: orderId },
        include: { orderItems: true },
      });

      if (!order) {
        throw new HttpError('Order not found', 404);
      }

      if (order.status !== 'DELIVERED') {
        throw new HttpError(
          'Order must be delivered before it can be received',
          400
        );
      }

      await db.$transaction(async (tx) => {
        await tx.cafeteriaSpecialOrder.update({
          where: { id: orderId },
          data: {
            status: 'RECEIVED',
            receivedBy: receivedBy,
            receivedAt: new Date(),
            receivedTotalAmount: totalAmount,
          },
        });

        for (const item of receivedItems) {
          await tx.cafeteriaOrderItem.updateMany({
            where: {
              specialOrderId: orderId,
            },
            data: {
              receivedQuantity: item.quantity,
              totalPrice: item.quantity * item.unitPrice,
            },
          });
        }

        const cafeteriaSystemAccount = await getCafeteriaSystemAccount();
        const companySystemAccount = await getMainSystemAccount();

        // Create transaction record
        await tx.transaction.create({
          data: {
            amount: new decimal(totalAmount),
            status: 'SUCCESS',
            type: 'CAFETERIA',
            locationId: Number(order.locationId),
            mode: 'Special',
            reference: order.orderNumber,
            remarks: `Special order received - ${order.purpose}`,
            fromAccountId: companySystemAccount?.account?.id,
            toAccountId: cafeteriaSystemAccount?.account?.id,
          },
        });
      });

      return { message: 'Order received successfully' };
    } catch (error) {
      logger.error('Error receiving special order:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to receive special order', 400);
    }
  },

  returnSpecialOrderItems: async (staffId: any, reqBody: any) => {
    try {
      const { orderId, returnedBy, reason, totalAmount, returnedItems } =
        reqBody;

      const order = await db.cafeteriaSpecialOrder.findUnique({
        where: { id: orderId },
        include: { orderItems: true },
      });

      if (!order) {
        throw new HttpError('Order not found', 404);
      }

      if (order.status !== 'RECEIVED') {
        throw new HttpError(
          'Order must be received before items can be returned',
          400
        );
      }

      await db.$transaction(async (tx) => {
        const returnedItemsDetails = [];

        for (const item of returnedItems) {
          const orderItem = order.orderItems.find(
            (oi) => oi.menuItemId === item.menuItemId
          );

          if (!orderItem) {
            throw new HttpError(
              `One or more menu item not found in order`,
              400
            );
          }

          const receivedQty = orderItem.receivedQuantity || 0;
          if (item.quantity > receivedQty) {
            throw new HttpError(
              `Cannot return ${item.quantity} items. Only ${receivedQty} were received`,
              400
            );
          }

          // Record the return
          await tx.cafeteriaReturnedItem.create({
            data: {
              specialOrderId: orderId,
              menuItemId: Number(item.menuItemId),
              returnedQuantity: item.quantity,
              reason: reason,
              returnedBy: returnedBy,
            },
            include: {
              menuItem: true,
            },
          });

          returnedItemsDetails.push(item.reason);
        }

        const transaction = await tx.transaction.findFirst({
          where: {
            reference: order.orderNumber,
          },
        });

        await tx.cafeteriaSpecialOrder.update({
          where: { id: orderId },
          data: {
            returnedTotalAmount: new decimal(totalAmount),
          },
        });

        // Update transaction record with returned amount
        if (transaction) {
          await tx.transaction.update({
            where: {
              id: transaction.id,
            },
            data: {
              amount: new decimal(totalAmount),
              remarks: `${transaction.remarks || ''}\n Return reason: ${reason}. Total Amount worth returned ( ${totalAmount})`,
            },
          });
        }
      });

      return { message: 'Items returned successfully' };
    } catch (error) {
      logger.error('Error returning special order items:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to return special order items', 400);
    }
  },

  getStaffSpecialOrders: async (staffId: any, query: any = {}) => {
    try {
      const page: number = parseInt(query.page as string) || 1;
      const limit: number = parseInt(query.limit as string) || 10;
      const search: string = (query.search as string) || '';
      const startDate = query.startDate as string;
      const endDate = query.endDate as string;
      const dateFilter = createDateFilter(startDate, endDate);

      const whereClause: any = {
        staffId: Number(staffId),
        ...(search
          ? {
              OR: [
                { orderNumber: { contains: search, mode: 'insensitive' } },
                { purpose: { contains: search, mode: 'insensitive' } },
              ],
            }
          : {}),
        ...dateFilter,
      };

      const [orders, totalCount] = await db.$transaction([
        db.cafeteriaSpecialOrder.findMany({
          where: whereClause,
          orderBy: { createdAt: 'desc' },
          skip: (page - 1) * limit,
          take: limit,
          include: {
            orderItems: {
              include: {
                menuItem: true,
              },
            },
            returnedItems: {
              include: {
                menuItem: true,
              },
            },
          },
        }),
        db.cafeteriaSpecialOrder.count({
          where: whereClause,
        }),
      ]);

      return {
        specialOrders: orders,
        totalPages: Math.ceil(totalCount / limit),
        totalCount: totalCount,
        currentPage: page,
        limit: limit,
      };
    } catch (error) {
      logger.error('Error getting staff special orders:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to fetch special orders', 400);
    }
  },
};
